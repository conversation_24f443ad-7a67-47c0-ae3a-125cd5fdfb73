const { saveTaskToDatabase, getTasksFromDatabase, deleteTaskFromDatabase } = require('./database-service');

// Helper function to create and save task
const createAndSaveTask = async (taskInfo, selectedFarm, assignee, userId, language) => {
  try {
    // Parse due date if provided
    let dueDate = null;
    if (taskInfo.dueDate) {
      // Simple date parsing - you might want to use a more sophisticated date parser
      const dateStr = taskInfo.dueDate.toLowerCase();
      const now = new Date();

      if (dateStr.includes('tomorrow')) {
        dueDate = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      } else if (dateStr.includes('next week')) {
        dueDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      } else if (dateStr.includes('friday')) {
        const friday = new Date(now);
        friday.setDate(now.getDate() + (5 - now.getDay() + 7) % 7);
        dueDate = friday;
      } else {
        // Try to parse as a regular date
        const parsed = new Date(taskInfo.dueDate);
        if (!isNaN(parsed.getTime())) {
          dueDate = parsed;
        }
      }
    }

    // Create task data matching the frontend structure
    const taskData = {
      title: taskInfo.title,
      description: taskInfo.description || '',
      farmId: selectedFarm.id,
      farmName: selectedFarm.name,
      assignedTo: assignee.id,
      assigneeName: assignee.name,
      assignedBy: userId,
      assignedByName: 'System', // You might want to get the actual user name
      dueDate: dueDate ? dueDate.getTime() : null,
      priority: taskInfo.priority || 'medium',
      status: 'pending',
      notes: taskInfo.notes || '',
      recurrence: taskInfo.recurrence || 'none',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to database
    const savedTask = await saveTaskToDatabase(taskData);

    const successMessage = language === 'ur' ?
      `✅  کام کامیابی سے بنایا گیا :

عنوان: ${taskData.title}
تفویض کردہ: ${assignee.name}
فارم: ${selectedFarm.name}
ترجیح: ${taskData.priority}
${dueDate ? `آخری تاریخ: ${dueDate.toLocaleDateString()}` : ''}
🆔 ڈیٹابیس ID: ${savedTask.id}

✅ کامیابی سے محفوظ ہو گیا!` :

      `✅ Task Created Successfully:

Title: ${taskData.title}
Assigned to: ${assignee.name}
Farm: ${selectedFarm.name}
Priority: ${taskData.priority}
${dueDate ? `Due Date: ${dueDate.toLocaleDateString()}` : ''}
🆔 Database ID: ${savedTask.id}
`;

    return {
      message: successMessage,
      taskData: savedTask,
      saved: true,
      databaseId: savedTask.id
    };
  } catch (error) {
    console.error('Error creating and saving task:', error);
    throw error;
  }
};
// Helper function to format expense description
const formatExpenseDescription = (receiptData) => {
  try {
    let description = '';

    // Add merchant info if available
    if (receiptData.merchantName) {
      description += `Shop: ${receiptData.merchantName}\n`;
    }

    // Add items if available
    if (receiptData.items && receiptData.items.length > 0) {
      description += 'Items:\n';
      receiptData.items.forEach(item => {
        description += `• ${item.name}`;
        if (item.quantity) description += ` (${item.quantity})`;
        if (item.totalPrice) description += ` - ${receiptData.currency || 'PKR'} ${item.totalPrice}`;
        description += '\n';
      });
    }

    // Add general description if available
    if (receiptData.description && receiptData.description !== description) {
      description += `\nDetails: ${receiptData.description}`;
    }

    // Add merchant address if available
    if (receiptData.merchantAddress) {
      description += `\nAddress: ${receiptData.merchantAddress}`;
    }

    return description.trim() || `Receipt from ${receiptData.merchantName || 'store'} - ${receiptData.currency || 'PKR'} ${receiptData.amount}`;
  } catch (error) {
    console.error('Error formatting expense description:', error);
    return `Receipt - ${receiptData.currency || 'PKR'} ${receiptData.amount}`;
  }
};


// Helper function to find tasks by title or assignee
const findTasksByQuery = (tasks, query) => {
  if (!query || !tasks || tasks.length === 0) {
    return tasks || [];
  }

  const lowerQuery = query.toLowerCase();
  return tasks.filter(task => {
    const titleMatch = task.title && task.title.toLowerCase().includes(lowerQuery);
    const assigneeMatch = task.assigneeName && task.assigneeName.toLowerCase().includes(lowerQuery);
    const descriptionMatch = task.description && task.description.toLowerCase().includes(lowerQuery);

    return titleMatch || assigneeMatch || descriptionMatch;
  });
};

// Helper function to prepare task selection list for frontend
const prepareTaskSelectionList = (tasks) => {
  return tasks.map(task => ({
    id: task.id,
    label: task.title,
    description: `${task.assigneeName ? `Assigned to: ${task.assigneeName}` : 'Unassigned'}${task.dueDate ? ` • Due: ${new Date(task.dueDate).toLocaleDateString()}` : ''}`,
    priority: task.priority,
    status: task.status,
    dueDate: task.dueDate,
    assigneeName: task.assigneeName
  }));
};

// Main function to handle task deletion
const deleteTask = async (deletionInfo, selectedFarm, userId, language) => {
  try {
    console.log('Processing task deletion:', deletionInfo);

    // Get all tasks for the farm
    const allTasks = await getTasksFromDatabase(selectedFarm.id);

    if (!allTasks || allTasks.length === 0) {
      const noTasksMessage = language === 'ur' ?
        '❌ اس فارم میں کوئی کام نہیں ملا۔' :
        '❌ No tasks found for this farm.';

      return {
        message: noTasksMessage,
        error: true
      };
    }

    // If specific task ID is provided, delete directly
    if (deletionInfo.taskId) {
      const taskToDelete = allTasks.find(task => task.id === deletionInfo.taskId);
      if (!taskToDelete) {
        const notFoundMessage = language === 'ur' ?
          '❌ مخصوص کام نہیں ملا۔' :
          '❌ Specified task not found.';

        return {
          message: notFoundMessage,
          error: true
        };
      }

      // Delete the task
      await deleteTaskFromDatabase(deletionInfo.taskId, selectedFarm.id, userId);

      const successMessage = language === 'ur' ?
        `✅ کام کامیابی سے ڈیلیٹ ہو گیا:

عنوان: ${taskToDelete.title}
تفویض کردہ: ${taskToDelete.assigneeName || 'غیر تفویض کردہ'}
فارم: ${selectedFarm.name}` :

        `✅ Task Deleted Successfully:

Title: ${taskToDelete.title}
Assigned to: ${taskToDelete.assigneeName || 'Unassigned'}
Farm: ${selectedFarm.name}`;

      return {
        message: successMessage,
        deleted: true,
        taskData: taskToDelete
      };
    }

    // If task title or assignee is specified, try to find matching tasks
    let matchingTasks = allTasks;

    if (deletionInfo.taskTitle) {
      matchingTasks = findTasksByQuery(allTasks, deletionInfo.taskTitle);
    }

    if (deletionInfo.assigneeName && matchingTasks.length > 1) {
      matchingTasks = matchingTasks.filter(task =>
        task.assigneeName && task.assigneeName.toLowerCase().includes(deletionInfo.assigneeName.toLowerCase())
      );
    }

    // If exactly one task matches, delete it directly
    if (matchingTasks.length === 1) {
      const taskToDelete = matchingTasks[0];
      await deleteTaskFromDatabase(taskToDelete.id, selectedFarm.id, userId);

      const successMessage = language === 'ur' ?
        `✅ کام کامیابی سے ڈیلیٹ ہو گیا:

عنوان: ${taskToDelete.title}
تفویض کردہ: ${taskToDelete.assigneeName || 'غیر تفویض کردہ'}
فارم: ${selectedFarm.name}` :

        `✅ Task Deleted Successfully:

Title: ${taskToDelete.title}
Assigned to: ${taskToDelete.assigneeName || 'Unassigned'}
Farm: ${selectedFarm.name}`;

      return {
        message: successMessage,
        deleted: true,
        taskData: taskToDelete
      };
    }

    // If multiple tasks match or no specific criteria, show selection list
    const tasksToShow = matchingTasks.length > 0 ? matchingTasks : allTasks;
    const taskList = prepareTaskSelectionList(tasksToShow);

    const selectionMessage = language === 'ur' ?
      `📋 کون سا کام ڈیلیٹ کرنا ہے؟ فہرست سے منتخب کریں:

${matchingTasks.length > 0 && matchingTasks.length < allTasks.length ?
  `میں نے "${deletionInfo.taskTitle || deletionInfo.assigneeName}" کے لیے ${matchingTasks.length} کام ملے ہیں:` :
  `${selectedFarm.name} میں ${allTasks.length} کام ہیں:`}

👇 کام منتخب کرنے کے لیے کلک کریں:` :

      `📋 Which task would you like to delete? Please select from the list:

${matchingTasks.length > 0 && matchingTasks.length < allTasks.length ?
  `I found ${matchingTasks.length} tasks matching "${deletionInfo.taskTitle || deletionInfo.assigneeName}":` :
  `There are ${allTasks.length} tasks in ${selectedFarm.name}:`}

👇 Click to select a task:`;

    return {
      message: selectionMessage,
      needsTaskSelection: true,
      taskList: taskList,
      selectionType: 'task_deletion',
      context: {
        deletionInfo,
        farmId: selectedFarm.id,
        farmName: selectedFarm.name,
        needsTaskSelection: true
      },
      error: false
    };

  } catch (error) {
    console.error('Error in task deletion:', error);
    throw error;
  }
};

module.exports = {
  createAndSaveTask,
  formatExpenseDescription,
  deleteTask,
  findTasksByQuery,
  prepareTaskSelectionList
};