// Firebase instances will be passed from the main app
let firestore = null;

const setFirestore = (firestoreInstance) => {
  firestore = firestoreInstance;
};

const saveAnimalToDatabase = async (animalData) => {
  try {

    // Clean data - remove undefined values
    const cleanData = {};
    Object.entries(animalData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanData[key] = value;
      }
    });

    // Add timestamps
    const timestamp = Date.now();
    cleanData.createdAt = timestamp;
    cleanData.updatedAt = timestamp;

    // Add to Firestore as subcollection under farm
    const farmRef = firestore.collection('farms').doc(cleanData.farmId);
    const animalCollectionRef = farmRef.collection('animals');
    const docRef = await animalCollectionRef.add(cleanData);

    // Update farm animal count
    const farmDoc = await farmRef.get();
    if (farmDoc.exists) {
      const farmData = farmDoc.data();
      await farmRef.update({
        animalCount: (farmData.animalCount || 0) + 1,
        updatedAt: timestamp
      });
    }

    // Log activity
    const activitiesRef = farmRef.collection('activities');
    await activitiesRef.add({
      farmId: cleanData.farmId,
      userId: cleanData.ownerId,
      action: 'Animal Added',
      description: `Added new ${cleanData.species.toLowerCase()}: ${cleanData.name} (${cleanData.tagId || 'No tag'})`,
      createdAt: new Date(),
      updatedAt: new Date(),
      appType: "2"
    });

    console.log('Animal saved with ID:', docRef.id);
    return { id: docRef.id, ...cleanData };
  } catch (error) {
    console.error('Error saving animal to database:', error);
    throw error;
  }
};

const saveFarmToDatabase = async (farmData) => {
  try {
    console.log('Saving farm to Firestore:', farmData);

    // Add to Firestore farms collection
    const farmRef = await firestore.collection('farms').add(farmData);
    console.log('Farm added to Firestore with ID:', farmRef.id);

    // Update user's assignedFarmIds
    if (farmData.ownerId) {
      const userRef = firestore.collection('users').doc(farmData.ownerId);
      const userDoc = await userRef.get();

      if (userDoc.exists) {
        const userData = userDoc.data();
        const assignedFarmIds = userData.assignedFarmIds || [];

        if (!assignedFarmIds.includes(farmRef.id)) {
          await userRef.update({
            assignedFarmIds: [...assignedFarmIds, farmRef.id]
          });
          console.log('Updated user assignedFarmIds');
        }
      }
    }

    return {
      id: farmRef.id,
      ...farmData
    };
  } catch (error) {
    console.error('Error saving farm to database:', error);
    throw error;
  }
};

const saveExpenseToDatabase = async (expenseData) => {
  try {
    console.log('Saving expense to database:', expenseData);

    // Clean data - remove undefined values
    const cleanData = {};
    Object.entries(expenseData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanData[key] = value;
      }
    });

    // Add to Firestore as subcollection under farm
    const farmRef = firestore.collection('farms').doc(cleanData.farmId);
    const expenseCollectionRef = farmRef.collection('expenses');
    const docRef = await expenseCollectionRef.add(cleanData);

    // Log activity
    const activitiesRef = farmRef.collection('activities');
    await activitiesRef.add({
      farmId: cleanData.farmId,
      userId: cleanData.createdBy,
      action: 'Expense Added',
      description: `Added expense: ${cleanData.category} - ${cleanData.currency} ${cleanData.amount}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      appType: "2"
    });

    console.log('Expense saved with ID:', docRef.id);
    return { id: docRef.id, ...cleanData };
  } catch (error) {
    console.error('Error saving expense to database:', error);
    throw error;
  }
};

const saveMilkingToDatabase = async (milkingData) => {
  try {
    console.log('Saving milking record to database:', milkingData);
    
    const milkingRef = collection(db, 'farms', milkingData.farmId, 'milking');
    const docRef = await addDoc(milkingRef, milkingData);
    
    console.log('Milking record saved with ID:', docRef.id);
    return { id: docRef.id, ...milkingData };
  } catch (error) {
    console.error('Error saving milking record to database:', error);
    throw error;
  }
};

const saveHealthCheckToDatabase = async (healthData) => {
  try {
    console.log('Saving health check to database:', healthData);

    const healthRef = collection(db, 'farms', healthData.farmId, 'healthChecks');
    const docRef = await addDoc(healthRef, healthData);

    console.log('Health check saved with ID:', docRef.id);
    return { id: docRef.id, ...healthData };
  } catch (error) {
    console.error('Error saving health check to database:', error);
    throw error;
  }
};

const savePregnancyToDatabase = async (pregnancyData) => {
  try {
    console.log('Saving pregnancy record to database:', pregnancyData);

    // Clean data - remove undefined values
    const cleanData = {};
    Object.entries(pregnancyData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanData[key] = value;
      }
    });

    // Add timestamps
    const timestamp = Date.now();
    cleanData.createdAt = new Date(timestamp).toISOString();
    cleanData.updatedAt = new Date(timestamp).toISOString();

    // Validate that we have an animal ID
    if (!cleanData.animalId) {
      throw new Error('Cannot save pregnancy record without a valid animal ID.');
    }

    // Add to Firestore as subcollection under the specific animal
    const farmRef = firestore.collection('farms').doc(cleanData.farmId);
    const animalRef = farmRef.collection('animals').doc(cleanData.animalId);
    const pregnancyCollectionRef = animalRef.collection('pregnancies');
    const docRef = await pregnancyCollectionRef.add(cleanData);

    // Update animal's pregnancy status if animalId is provided
    try {
      const animalDoc = await animalRef.get();

      if (animalDoc.exists) {
        await animalRef.update({
          pregnancyStatus: cleanData.status,
          lastPregnancyDate: cleanData.conceptionDate,
          isPregnant: cleanData.status === 'confirmed' || cleanData.status === 'suspected',
          updatedAt: timestamp
        });
        console.log('Updated animal pregnancy status');
      }
    } catch (animalUpdateError) {
      console.warn('Could not update animal pregnancy status:', animalUpdateError);
      // Don't fail the pregnancy save if animal update fails
    }

    // Log activity
    const activitiesRef = farmRef.collection('activities');
    await activitiesRef.add({
      farmId: cleanData.farmId,
      userId: cleanData.createdBy,
      action: 'Pregnancy Recorded',
      description: `Recorded pregnancy for ${cleanData.animalName}: ${cleanData.status} ${cleanData.isAICross ? '(AI Cross)' : cleanData.sireName ? `(Sired by ${cleanData.sireName})` : ''}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      appType: "2"
    });

    console.log('Pregnancy saved with ID:', docRef.id);
    return { id: docRef.id, ...cleanData };
  } catch (error) {
    console.error('Error saving pregnancy to database:', error);
    throw error;
  }
};

const saveTaskToDatabase = async (taskData) => {
  try {
    console.log('Saving task to database:', taskData);

    // Clean data - remove undefined values
    const cleanData = {};
    Object.entries(taskData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        cleanData[key] = value;
      }
    });

    // Add timestamps
    const timestamp = Date.now();
    cleanData.createdAt = new Date(timestamp);
    cleanData.updatedAt = new Date(timestamp);

    // Validate required fields
    if (!cleanData.farmId) {
      throw new Error('Cannot save task without a valid farm ID.');
    }
    if (!cleanData.title) {
      throw new Error('Cannot save task without a title.');
    }
    if (!cleanData.assignedTo) {
      throw new Error('Cannot save task without an assignee.');
    }

    // Add to Firestore as subcollection under farm
    const farmRef = firestore.collection('farms').doc(cleanData.farmId);
    const taskCollectionRef = farmRef.collection('tasks');
    const docRef = await taskCollectionRef.add(cleanData);

    // Log activity
    const activitiesRef = farmRef.collection('activities');
    await activitiesRef.add({
      farmId: cleanData.farmId,
      userId: cleanData.assignedBy,
      action: 'Task Created',
      description: `Created task "${cleanData.title}" for ${cleanData.assigneeName}${cleanData.dueDate ? ` (Due: ${new Date(cleanData.dueDate).toLocaleDateString()})` : ''}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      appType: "2"
    });

    console.log('Task saved with ID:', docRef.id);
    return { id: docRef.id, ...cleanData };
  } catch (error) {
    console.error('Error saving task to database:', error);
    throw error;
  }
};

const deleteFarmFromDatabase = async (farmId, userId) => {
  try {
    console.log('Deleting farm from database:', farmId);

    // Get farm reference
    const farmRef = firestore.collection('farms').doc(farmId);
    const farmDoc = await farmRef.get();

    if (!farmDoc.exists) {
      throw new Error('Farm not found');
    }

    const farmData = farmDoc.data();

    // Get user data to check role and permissions
    const userRef = firestore.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      throw new Error('User not found');
    }

    const userData = userDoc.data();
    const userRole = userData.role;
    const assignedFarmIds = userData.assignedFarmIds || [];

    // Check if user has permission to delete
    let hasPermission = false;

    if (userRole === 'owner') {
      // Owners can delete farms they own or are assigned to
      hasPermission = farmData.ownerId === userId || assignedFarmIds.includes(farmId);
    } else if (userRole === 'admin') {
      // Admins can delete farms they are assigned to
      hasPermission = assignedFarmIds.includes(farmId);
    }

    if (!hasPermission) {
      throw new Error('Permission denied: Only farm owner or assigned admin can delete the farm');
    }

    // Delete all subcollections first
    const subcollections = ['animals', 'expenses', 'milking', 'healthChecks', 'tasks', 'activities', 'pregnancies'];

    for (const subcollectionName of subcollections) {
      const subcollectionRef = farmRef.collection(subcollectionName);
      const snapshot = await subcollectionRef.get();

      // Delete all documents in the subcollection
      const batch = firestore.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      if (snapshot.docs.length > 0) {
        await batch.commit();
        console.log(`Deleted ${snapshot.docs.length} documents from ${subcollectionName}`);
      }
    }

    // Remove farm ID from user's assignedFarmIds
    if (farmData.ownerId) {
      const userRef = firestore.collection('users').doc(farmData.ownerId);
      const userDoc = await userRef.get();

      if (userDoc.exists) {
        const userData = userDoc.data();
        const assignedFarmIds = userData.assignedFarmIds || [];
        const updatedFarmIds = assignedFarmIds.filter(id => id !== farmId);

        await userRef.update({
          assignedFarmIds: updatedFarmIds
        });
        console.log('Updated user assignedFarmIds after farm deletion');
      }
    }

    // Remove farm from all users who have it assigned
    const usersSnapshot = await firestore.collection('users')
      .where('assignedFarmIds', 'array-contains', farmId)
      .get();

    const userUpdateBatch = firestore.batch();
    usersSnapshot.docs.forEach(userDoc => {
      const userData = userDoc.data();
      const updatedFarmIds = (userData.assignedFarmIds || []).filter(id => id !== farmId);
      userUpdateBatch.update(userDoc.ref, { assignedFarmIds: updatedFarmIds });
    });

    if (usersSnapshot.docs.length > 0) {
      await userUpdateBatch.commit();
      console.log(`Updated ${usersSnapshot.docs.length} users' assignedFarmIds`);
    }

    // Finally, delete the farm document
    await farmRef.delete();
    console.log('Farm deleted successfully:', farmId);

    return {
      id: farmId,
      name: farmData.name,
      deleted: true
    };
  } catch (error) {
    console.error('Error deleting farm from database:', error);
    throw error;
  }
};

const formatFarmDescription = (farmData) => {
  const parts = [];

  if (farmData.farmName) parts.push(`Farm: ${farmData.farmName}`);
  if (farmData.farmType) parts.push(`Type: ${farmData.farmType}`);
  if (farmData.farmSize && farmData.sizeUnit) parts.push(`Size: ${farmData.farmSize} ${farmData.sizeUnit}`);
  if (farmData.farmLocation) parts.push(`Location: ${farmData.farmLocation}`);

  return parts.join(', ');
};

// Get all tasks for a specific farm
const getTasksFromDatabase = async (farmId) => {
  try {
    console.log('Getting tasks for farm:', farmId);

    if (!farmId) {
      throw new Error('farmId is required to get tasks');
    }

    const farmRef = firestore.collection('farms').doc(farmId);
    const tasksCollectionRef = farmRef.collection('tasks');
    const snapshot = await tasksCollectionRef.orderBy('createdAt', 'desc').get();

    const tasks = [];
    snapshot.forEach(doc => {
      tasks.push({
        id: doc.id,
        ...doc.data()
      });
    });

    console.log(`Found ${tasks.length} tasks for farm ${farmId}`);
    return tasks;
  } catch (error) {
    console.error('Error getting tasks from database:', error);
    throw error;
  }
};

// Delete a specific task from database
const deleteTaskFromDatabase = async (taskId, farmId, userId) => {
  try {
    console.log('Deleting task:', taskId, 'from farm:', farmId);

    if (!taskId || !farmId) {
      throw new Error('taskId and farmId are required to delete a task');
    }

    // Get task details before deletion for activity logging
    const farmRef = firestore.collection('farms').doc(farmId);
    const taskRef = farmRef.collection('tasks').doc(taskId);
    const taskDoc = await taskRef.get();

    if (!taskDoc.exists) {
      throw new Error('Task not found');
    }

    const taskData = taskDoc.data();

    // Delete the task
    await taskRef.delete();

    // Log activity
    const activitiesRef = farmRef.collection('activities');
    await activitiesRef.add({
      farmId: farmId,
      userId: userId,
      action: 'Task Deleted',
      description: `Deleted task "${taskData.title}"${taskData.assigneeName ? ` (was assigned to ${taskData.assigneeName})` : ''}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      appType: "2"
    });

    console.log('Task deleted successfully:', taskId);
    return { id: taskId, ...taskData };
  } catch (error) {
    console.error('Error deleting task from database:', error);
    throw error;
  }
};

module.exports = {
  setFirestore,
  saveAnimalToDatabase,
  saveFarmToDatabase,
  saveExpenseToDatabase,
  saveMilkingToDatabase,
  saveHealthCheckToDatabase,
  savePregnancyToDatabase,
  saveTaskToDatabase,
  getTasksFromDatabase,
  deleteTaskFromDatabase,
  deleteFarmFromDatabase,
  formatFarmDescription
};
