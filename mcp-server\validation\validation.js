const validateRequest = (req, openai) => {
  const { prompt, imageUri, language = 'en' } = req.body;

  if (!prompt && !imageUri) {
    return {
      status: 400,
      json: {
        error: 'Prompt or image is required',
        message: language === 'ur' ? 'پیغام یا تصویر درکار ہے' : 'Message or image is required'
      }
    };
  }

  if (!openai) {
    console.log('ERROR: OpenAI client not initialized');
    return {
      status: 500,
      json: { error: 'OpenAI client not initialized' }
    };
  }

  return null; // No error
};

module.exports = {
  validateRequest
};